import { MicVAD } from "@ricky0123/vad-web";

let mediaRecorder;
let audioChunks = [];
let audioContext;
let audioSource;
let audioProcessor;
let isRecording = false;
let chunkInterval;
let pcmBuffer = [];
let fullPcmBuffer = [];
let currentChunkNumber = 1;
let sessionId = null;
let isListening = false;
let isReplying = false;
let vad = null;
let stream = null;

const SAMPLE_RATE = 24000;
const CHUNK_DURATION = 5000;

const recordButton = document.getElementById('recordButton');
const sendButton = document.getElementById('sendButton');
const messageInput = document.getElementById('messageInput');
const recordingIndicator = document.getElementById('recordingIndicator');
const startInterviewBtn = document.getElementById('startInterviewBtn');
const connectionStatus = document.getElementById('connectionStatus');

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    // Hide text input functionality since we're using voice only
    // document.querySelector('.flex-1.bg-gray-100').style.display = 'none';
    // sendButton.style.display = 'none';
    
    // Initialize UI state
    updateUIState('ready');
});

// UI State Management
function updateUIState(state) {
    switch (state) {
        case 'ready':
            startInterviewBtn.innerHTML = '<i class="fas fa-play text-xl"></i>';
            startInterviewBtn.disabled = false;
            recordButton.classList.add('hidden');
            connectionStatus.textContent = 'Ready to Start';
            connectionStatus.className = 'text-xs flex items-center';
            break;
        case 'connecting':
            startInterviewBtn.disabled = true;
            recordButton.classList.add('hidden');
            connectionStatus.textContent = 'Connecting...';
            connectionStatus.className = 'text-xs flex items-center text-yellow-400';
            break;
        case 'connected':
            startInterviewBtn.innerHTML = '<i class="fas fa-stop text-xl"></i>';
            startInterviewBtn.disabled = false;
            recordButton.classList.add('hidden');
            connectionStatus.textContent = 'Connected';
            connectionStatus.className = 'text-xs flex items-center text-green-400';
            break;
        case 'recording':
            startInterviewBtn.disabled = false;
            recordButton.classList.add('hidden');
            connectionStatus.textContent = 'Recording...';
            connectionStatus.className = 'text-xs flex items-center text-red-400';
            break;
    }
}

// Start Interview Button Handler
startInterviewBtn.addEventListener('click', async () => {
    if (!isListening) {
        try {
            updateUIState('connecting');
            await startInterviewSession();
            startInterviewBtn.innerHTML = '<i class="fas fa-stop text-xl"></i>';
            recordingIndicator.innerHTML = '<i class="fas fa-circle"></i> <span class="text-xs">Listening...</span>';
            recordingIndicator.classList.remove('hidden');
            isListening = true;
            await startListening();
        } catch (error) {
            console.error('Failed to start interview:', error);
            alert('Failed to start interview. Please try again.');
            updateUIState('ready');
        }
    } else {
        stopListening();
        startInterviewBtn.innerHTML = '<i class="fas fa-play text-xl"></i>';
        recordingIndicator.classList.add('hidden');
        isListening = false;
        updateUIState('ready');
    }
});

// Start Interview Session
async function startInterviewSession() {
    try {
        const response = await fetch('http://127.0.0.1:8000/start-session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to start session');
        }

        const data = await response.json();
        sessionId = data.session_id;
        
        updateUIState('connected');
        addBotMessage('Apakah kamu siap?');
    } catch (error) {
        throw error;
    }
}

// Record button functionality - now push-to-talk
// recordButton.addEventListener('click', async () => { ... });

async function startListening() {
    stream = await navigator.mediaDevices.getUserMedia({ 
        audio: { 
            noiseSuppression: true, 
            echoCancellation: true 
        } 
    });
    
    vad = await MicVAD.new({
        model: "v5",
        positiveSpeechThreshold: 0.4,
        negativeSpeechThreshold: 0.4,
        minSpeechFrames: 15,
        preSpeechPadFrames: 30,
        stream: stream,
        onSpeechStart: () => {
            if (!isReplying && !isRecording) {
                console.log("Speech started");
                isRecording = true;
                updateUIState('recording');
                recordingIndicator.innerHTML = '<i class="fas fa-circle"></i> <span class="text-xs">Recording...</span>';
                startRecording(stream);
            }
        },
        onSpeechEnd: () => {
            console.log("Speech ended");
            stopRecording();
            isReplying = true;
            updateUIState('connected');
            recordingIndicator.innerHTML = '<i class="fas fa-circle"></i> <span class="text-xs">Processing...</span>';
        }
    });

    await vad.start();
}

function stopListening() {
    if (vad) {
        vad.pause();
        vad = null;
    }
    if (stream) {
        stream.getTracks().forEach(track => track.stop());
        stream = null;
    }
    if (isRecording) {
        stopRecording();
    }
    isListening = false;
    isRecording = false;
}

// Start recording from the stream
async function startRecording(stream) {
    audioContext = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: SAMPLE_RATE
    });
    audioSource = audioContext.createMediaStreamSource(stream);
    pcmBuffer = [];
    fullPcmBuffer = [];
    
    audioProcessor = audioContext.createScriptProcessor(4096, 1, 1);
    
    audioProcessor.onaudioprocess = (e) => {
        const inputData = e.inputBuffer.getChannelData(0);
        const pcmData = new Int16Array(inputData.length);
        
        for (let i = 0; i < inputData.length; i++) {
            pcmData[i] = Math.max(-32768, Math.min(32767, Math.floor(inputData[i] * 32767)));
        }
        
        pcmBuffer.push(...Array.from(pcmData));
        fullPcmBuffer.push(...Array.from(pcmData));
    };
    
    audioSource.connect(audioProcessor);
    audioProcessor.connect(audioContext.destination);
    
    chunkInterval = setInterval(() => {
        if (pcmBuffer.length > 0) {
            const samplesPerChunk = Math.floor(SAMPLE_RATE * (CHUNK_DURATION / 1000));
            const chunkSize = Math.min(samplesPerChunk, pcmBuffer.length);
            const chunk = pcmBuffer.splice(0, chunkSize);
            
            const wavBlob = convertPCMToWAV(chunk);
            processAudioChunk(wavBlob, false);
        }
    }, CHUNK_DURATION);
}

// Stop the current recording
function stopRecording() {
    if (isRecording) {
        // Clear the chunk processing interval
        clearInterval(chunkInterval);
        
        // Process any remaining audio in the buffer
        if (pcmBuffer.length > 0) {
            const wavBlob = convertPCMToWAV(pcmBuffer);
            processAudioChunk(wavBlob, true);
        }
        
        // Clean up audio context resources
        if (audioProcessor && audioSource) {
            audioProcessor.disconnect();
            audioSource.disconnect();
        }
        
        // Create and add full audio message
        const fullWavBlob = convertPCMToWAV(fullPcmBuffer);
        const fullAudioUrl = URL.createObjectURL(fullWavBlob);
        addFullAudioMessage(fullAudioUrl);
        
        isRecording = false;
        updateUIState('connected');
        recordingIndicator.innerHTML = '<i class="fas fa-circle"></i> <span class="text-xs">Press to talk...</span>';
    }
}

// Convert PCM samples to a WAV file format with proper headers
function convertPCMToWAV(pcmSamples) {
    const outputSampleRate = 24000;
    const numChannels = 1;
    const bitsPerSample = 16;
    const bytesPerSample = bitsPerSample / 8;
    const blockAlign = numChannels * bytesPerSample;
    const byteRate = outputSampleRate * blockAlign;
    const dataSize = pcmSamples.length * bytesPerSample;
    const bufferSize = 44 + dataSize;
    
    const buffer = new ArrayBuffer(bufferSize);
    const view = new DataView(buffer);
    
    writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + dataSize, true);
    writeString(view, 8, 'WAVE');
    
    writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, outputSampleRate, true);
    view.setUint32(28, byteRate, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitsPerSample, true);
    
    writeString(view, 36, 'data');
    view.setUint32(40, dataSize, true);
    
    let offset = 44;
    for (let i = 0; i < pcmSamples.length; i++) {
        view.setInt16(offset, pcmSamples[i], true);
        offset += 2;
    }
    
    return new Blob([buffer], { type: 'audio/wav' });
}

// Helper function to write strings to DataView
function writeString(view, offset, string) {
    for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
    }
}

// Add audio message to chat
function addAudioMessage(url) {
    const chatMessages = document.getElementById('chatMessages');
    const now = new Date();
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message user-message flex mb-4';
    
    messageDiv.innerHTML = `
        <div class="bg-blue-500 rounded-lg rounded-tr-none p-3 max-w-[80%] flex items-center">
            <audio controls src="${url}" class="flex-grow mr-3"></audio>
            <span class="text-xs text-blue-200">${time}</span>
        </div>
        <div class="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center ml-2 flex-shrink-0">
            <i class="fas fa-user text-white text-xs"></i>
        </div>
    `;
    
    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

function addTranscriptMessage(chatId, chunkNumber, message = '') {
    const chatMessages = document.getElementById('chatMessages');
    const now = new Date();
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message user-message flex mb-4';
    messageDiv.id = `transcriptMessage-${chatId}-${chunkNumber}`;
    
    messageDiv.innerHTML = `
        <div class="bg-blue-500 rounded-lg rounded-tr-none p-3 max-w-[80%] flex items-center">
            <p class="flex-grow text-blue-200">${message}</p>
            <span class="text-xs text-blue-200">${time}</span>
        </div>
        <div class="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center ml-2 flex-shrink-0">
            <i class="fas fa-user text-white text-xs"></i>
        </div>
    `;
    
    chatMessages.appendChild(messageDiv);
}

function appendToTranscriptMessage(transcript, chatId, chunkNumber) {
    const messageDiv = document.getElementById(`transcriptMessage-${chatId}-${chunkNumber}`);
    const currentText = messageDiv.querySelector('p').textContent
    messageDiv.querySelector('p').textContent = currentText + transcript;
}

// ----- New timer functionality for FullAudioMessage -----
let fullRecordingTimerInterval;
let fullRecordingStartTime;

function startFullRecordingTimer() {
    fullRecordingStartTime = Date.now();
    fullRecordingTimerInterval = setInterval(() => {
        const elapsed = Date.now() - fullRecordingStartTime;
        const seconds = (elapsed / 1000).toFixed(1);
        const timerElem = document.getElementById('fullRecordingTimer');
        if (timerElem) {
            timerElem.textContent = `${seconds} s`;
        }
    }, 100);
}

function stopFullRecordingTimer() {
    clearInterval(fullRecordingTimerInterval);
    const elapsed = Date.now() - fullRecordingStartTime;
    const seconds = (elapsed / 1000).toFixed(1);
    const timerElem = document.getElementById('fullRecordingTimer');
    if (timerElem) {
        timerElem.textContent = `${seconds} s`;
    }
}
// ---------------------------------------------------------

// Modify addFullAudioMessage to ensure unique timer element
function addFullAudioMessage(url) {
    // Remove id from any existing timer element to avoid duplicates
    const existingTimer = document.getElementById('fullRecordingTimer');
    if(existingTimer) {
        existingTimer.removeAttribute('id');
    }
    
    const chatMessages = document.getElementById('chatMessages');
    const now = new Date();
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message user-message flex mb-4';
    
    messageDiv.innerHTML = `
        <div class="bg-green-500 rounded-lg rounded-tr-none p-3 max-w-[80%] flex flex-col">
            <span class="text-xs text-green-100 mb-1">Full Recording - ${time}</span>
            <span class="recording-timer" id="fullRecordingTimer">0:00</span>
            <audio controls src="${url}" class="flex-grow"></audio>
        </div>
        <div class="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center ml-2 flex-shrink-0">
            <i class="fas fa-user text-white text-xs"></i>
        </div>
    `;
    
    chatMessages.appendChild(messageDiv);
    scrollToBottom();
    startFullRecordingTimer();
}

function addBotMessage(message) {
    const chatMessages = document.getElementById('chatMessages');
    const now = new Date();
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message bot-message flex mb-4';

    messageDiv.innerHTML = `
        <div class="w-8 h-8 rounded-full bg-blue-400 flex items-center justify-center mr-2 flex-shrink-0">
            <i class="fas fa-robot text-white text-xs"></i>
        </div>

        <div class="bg-gray-200 rounded-lg rounded-tl-none p-3 max-w-[80%]">
            <p class="text-gray-800">${message}</p>
            <span class="text-xs text-gray-500">${time}</span>
        </div>
    `;
    
    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

function appendToBotMessage(message) {
    const messageDiv = document.querySelector('.bot-message:last-child p');
    messageDiv.textContent += message;
}

// Scroll to the bottom of the chat
function scrollToBottom() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Send text message function kept for compatibility
function sendTextMessage() {
    // Text functionality is disabled in voice-only mode
}

// Process each audio chunk
function processAudioChunk(chunk, isFinal = false) {
    const chunkNumber = currentChunkNumber; // capture current chunk number
    const audioBlob = new Blob([chunk], { type: 'audio/wav' });
    // create unique audio url for each chunk
    const audioUrl = URL.createObjectURL(audioBlob);
    convertToBase64AndSendToAPI(audioBlob, chunkNumber, isFinal);
    currentChunkNumber++; // increment for next chunk
}

// Convert audio blob to base64 and send to API
function convertToBase64AndSendToAPI(audioBlob, chunkNumber, isFinal = false) {
    const reader = new FileReader();
    reader.onloadend = () => {
        // Extract base64 string (remove the data URL prefix)
        const base64Data = reader.result.split(',')[1];
        console.log(chunkNumber, base64Data);
        // Send to API with chunk number
        sendAudioToAPI(base64Data, chunkNumber, sessionId, isFinal);
    };
    reader.readAsDataURL(audioBlob);
}

// Send audio data to API
// response is in sse format
async function sendAudioToAPI(base64AudioData, chunkNumber = 1, sessionId = 12, isFinal = false) {
    const streamParams = {
        audio_data: base64AudioData,
        stream_type: 'chunk',
        chunk_number: chunkNumber,
        session_id: sessionId
    }

    const finalParams = {
        audio_data: base64AudioData,
        stream_type: 'final',
        chunk_number: chunkNumber,
        session_id: sessionId
    }

    const usedParams = isFinal ? finalParams : streamParams;

    const response = await fetch('http://127.0.0.1:8000/stream', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Transfer-Encoding': 'chunked',
            'X-Accel-Buffering': 'no'
        },
        body: JSON.stringify(usedParams),
    });

    if (!response.ok) {
        console.error('Error sending audio:', response.statusText);
        stopFullRecordingTimer();
        return;
    }

    if (!isFinal) {
        return;
    }

    addBotMessage('')
    const reader = response.body.pipeThrough(new TextDecoderStream()).getReader();
    let firstMessage = false;
    let buffer = '';
    
    try {
        while (true) {
            const { value, done } = await reader.read();
            
            if (done) {
                if (buffer.trim()) {
                    await processSSEMessage(buffer.trim(), firstMessage);
                }
                isReplying = false;
                console.log('Stream completed normally');
                break;
            }

            if (!firstMessage) {
                stopFullRecordingTimer();
                firstMessage = true;
            }

            // Append new data to buffer
            buffer += value;
            
            // Split buffer into lines
            const lines = buffer.split('\n');
            // Keep the last (potentially incomplete) line in the buffer
            buffer = lines.pop() || '';
            
            // Process complete lines
            for (const line of lines) {
                if (line.trim()) {
                    // Wait for each message to be processed before moving to the next
                    await processSSEMessage(line.trim(), firstMessage);
                }
            }
        }
    } catch (error) {
        console.error('Stream processing error:', error);
    } finally {
        reader.releaseLock();
    }
}

// Modify processSSEMessage to handle reply completion
async function processSSEMessage(message, firstMessage) {
    if (message.startsWith('data:')) {
        const data = message.substring(5);
        
        if (data === 'end') {
            console.log('Received end of stream signal');
            if (isListening) {
                recordingIndicator.innerHTML = '<i class="fas fa-circle"></i> <span class="text-xs">Listening...</span>';
            }
            return;
        }
        
        try {
            const jsonData = JSON.parse(data);
            
            if (jsonData.type === 'text') {
                await appendToBotMessage(jsonData.content);
            } else if (jsonData.type === 'audio') {
                // Wait for audio chunk to be queued
                await audioQueue.addChunk(jsonData.content);
            }
        } catch (error) {
            console.error('Error processing message:', error);
            console.log('Problematic message:', message);
        }
    }
}

// Modify AudioQueue to ensure sequential processing
class AudioQueue {
    constructor() {
        this.queue = [];
        this.isPlaying = false;
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        this.processingPromise = Promise.resolve();
        this.bufferSize = 3; // Number of chunks to buffer before playing
        this.sampleRate = 24000;
    }

    async addChunk(base64Audio) {
        await this.processingPromise;
        
        this.processingPromise = (async () => {
            const pcmString = atob(base64Audio);
            if (!isPCM16(pcmString)) {
                console.error('Invalid PCM data received');
                return;
            }
            
            this.queue.push(pcmString);
            
            // Start playing only when we have enough chunks buffered
            if (!this.isPlaying && this.queue.length >= this.bufferSize) {
                await this.playBufferedAudio();
            }
        })();

        return this.processingPromise;
    }

    async playBufferedAudio() {
        if (this.queue.length === 0) {
            this.isPlaying = false;
            return;
        }

        this.isPlaying = true;

        try {
            // Combine multiple chunks into one buffer
            const chunksToPlay = this.queue.splice(0, this.bufferSize);
            const combinedPCM = chunksToPlay.join('');
            
            const samples = combinedPCM.length / 2;
            const audioBuffer = this.audioContext.createBuffer(1, samples, this.sampleRate);
            const channelData = audioBuffer.getChannelData(0);
            
            // Convert binary string to 16-bit PCM and normalize to [-1, 1]
            for (let i = 0; i < samples; i++) {
                const byte1 = combinedPCM.charCodeAt(i * 2) & 0xFF;
                const byte2 = combinedPCM.charCodeAt(i * 2 + 1) & 0xFF;
                let sample = (byte2 << 8) | byte1;
                if (sample > 32767) {
                    sample = sample - 65536;
                }
                channelData[i] = sample / 32768.0;
            }

            const source = this.audioContext.createBufferSource();
            source.buffer = audioBuffer;
            source.connect(this.audioContext.destination);
            
            // Create a promise that resolves when the audio finishes playing
            await new Promise((resolve) => {
                source.onended = () => {
                    if (this.queue.length >= this.bufferSize) {
                        this.playBufferedAudio();
                    } else if (this.queue.length > 0) {
                        // Play remaining chunks if less than buffer size
                        this.playBufferedAudio();
                    } else {
                        this.isPlaying = false;
                    }
                    resolve();
                };
                
                source.start();
            });
        } catch (error) {
            console.error('Error playing audio chunks:', error);
            this.isPlaying = false;
            if (this.queue.length > 0) {
                await this.playBufferedAudio();
            }
        }
    }

    // Add method to handle final chunks
    async playFinalChunks() {
        if (this.queue.length > 0) {
            await this.playBufferedAudio();
        }
    }
}

// Create a single instance of AudioQueue
const audioQueue = new AudioQueue();

// Add new PCM playback functions
function isPCM16(pcmString) {
    // PCM 16-bit should have even length since each sample is 2 bytes
    return pcmString.length % 2 === 0;
}
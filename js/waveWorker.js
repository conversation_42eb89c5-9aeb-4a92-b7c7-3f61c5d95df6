// waveWorker.js - Web Worker for processing WAV files
self.onmessage = event => {
    const { arrayBuffer, sampleRate, numberOfChannels } = event.data;
    const view = new DataView(arrayBuffer);

    let offset = 44;
    for (let i = 0; i < (arrayBuffer.byteLength - 44) / (numberOfChannels * 2); i++) {
        for (let channel = 0; channel < numberOfChannels; channel++) {
            const sample = view.getInt16(offset, true) / 0x7FFF;
            view.setInt16(offset, sample * 0x7FFF, true);
            offset += 2;
        }
    }

    self.postMessage(arrayBuffer);
};

function writeString(view, offset, string) {
    for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
    }
}
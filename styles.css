/* ...existing code... */

.audio-message {
    display: flex;
    align-items: center;
}

.waveform {
    width: 100px;
    height: 20px;
    background: url('waveform-placeholder.png') no-repeat center center;
    background-size: cover;
    margin-right: 10px;
}

.recording-interface {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    border-top: 1px solid #ccc;
}

.cancel-button, .microphone-button, .send-button {
    background-color: #f0f0f0;
    border: none;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
}

.microphone-button {
    background-color: #6200ea;
    color: white;
}

.waveform {
    flex-grow: 1;
    height: 20px;
    background: url('waveform-placeholder.png') no-repeat center center;
    background-size: cover;
    margin: 0 10px;
}

.recording-timer {
    font-size: 14px;
    color: #666;
}

/* ...existing code... */

body {
    background-color: #f0f2f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
}

.chat-messages {
    height: 500px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #b3e5fc;
    background-color: #f1faff;
    margin-bottom: 20px;
}

#chatMessages {
    scrollbar-width: thin;
    scrollbar-color: rgba(0,0,0,0.2) transparent;
}

#chatMessages::-webkit-scrollbar {
    width: 6px;
}

#chatMessages::-webkit-scrollbar-track {
    background: transparent;
}

#chatMessages::-webkit-scrollbar-thumb {
    background-color: rgba(0,0,0,0.2);
    border-radius: 20px;
}

.chat-message {
    max-width: 100%;
    margin-bottom: 10px;
    display: flex;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message > div:last-child {
    background-color: #dcf8c6;
    border-radius: 18px 18px 0 18px;
    margin-right: 10px;
}

.bot-message > div:last-child {
    border-radius: 18px 18px 18px 0;
}

.record-section {
    text-align: center;
}

audio {
    display: block;
    width: 250px;
    margin-bottom: 8px;
    border-radius: 20px;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.animate-pulse {
    animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

#messageInput {
    border: none;
}

#messageInput:focus {
    outline: none;
}

#recordButton {
    position: relative;
    overflow: hidden;
    transition: background-color 0.3s, transform 0.1s;
}

#recordButton:active {
    transform: scale(0.95);
}

#recordButton::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200%;
    height: 200%;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.3s;
}

#recordButton:active::before {
    transform: translate(-50%, -50%) scale(1);
}

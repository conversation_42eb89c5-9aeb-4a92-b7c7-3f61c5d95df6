<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Interview</title>
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="css/styles.css" rel="stylesheet">
</head>
<body class="bg-gray-100 h-screen flex items-center justify-center">
    <div class="container max-w-4xl bg-white rounded-lg shadow-lg overflow-hidden flex flex-col h-[90vh]">
        <!-- Chat Header -->
        <div class="bg-blue-600 text-white px-4 py-3 flex items-center">
            <div class="flex items-center">
                <div class="w-10 h-10 rounded-full bg-blue-300 flex items-center justify-center mr-3">
                    <i class="fas fa-headset"></i>
                </div>
                <div>
                    <h1 class="font-bold text-lg">Audio Interview</h1>
                    <div class="text-xs flex items-center">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        <span id="connectionStatus">Ready to Start</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Chat Messages Section -->
        <div id="chatMessages" class="flex-1 overflow-y-auto p-4 bg-gray-50">
            <!-- Chat messages will be appended here -->
            <div class="chat-message-time text-center text-xs text-gray-500 my-2">Today</div>
            <div class="chat-message bot-message flex mb-4">
                <div class="w-8 h-8 rounded-full bg-blue-400 flex items-center justify-center mr-2 flex-shrink-0">
                    <i class="fas fa-robot text-white text-xs"></i>
                </div>
                <div class="bg-gray-200 rounded-lg rounded-tl-none p-3 max-w-[80%]">
                    <p class="text-gray-800">Welcome to the Audio Interview! Click the "Start Interview" button to begin.</p>
                    <span class="text-xs text-gray-500">09:41</span>
                </div>
            </div>
        </div>
        
        <!-- Recording Controls -->
        <div class="border-t border-gray-300 p-3 bg-white">
            <div class="flex items-center justify-center">
                <div id="recordingControls" class="flex items-center">
                    <div id="recordingIndicator" class="hidden mr-4 text-red-500 animate-pulse">
                        <i class="fas fa-circle"></i> <span class="text-sm">Recording...</span>
                    </div>
                    <button id="startInterviewBtn" class="p-4 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg transform transition-transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-play text-xl"></i>
                    </button>
                    <button id="recordButton" class="hidden p-4 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg transform transition-transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        <i class="fas fa-microphone text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script type="module" src="js/script.js"></script>
</body>
</html>
